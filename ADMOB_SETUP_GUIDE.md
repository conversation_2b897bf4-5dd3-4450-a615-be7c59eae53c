# 📋 دليل إعداد إعلانات AdMob

## 🎯 **الخطوات المطلوبة لتفعيل الإعلانات**

### **الخطوة 1: إعداد AdMob Console**

#### **1.1 إنشاء تطبيق في AdMob:**
1. اذهب إلى [AdMob Console](https://apps.admob.com/)
2. انقر "Add App"
3. اختر "Android"
4. أدخل معلومات التطبيق:
   - **App Name**: World Costs
   - **Package Name**: com.foodcana.worldcost
   - **Store**: Google Play Store

#### **1.2 إنشاء Ad Units:**

##### **Banner Ad Unit:**
- **Ad Format**: Banner
- **Ad Unit Name**: World Costs Banner
- **Expected Ad Unit ID**: ca-app-pub-3799584967407983/6300978111

##### **Interstitial Ad Unit:**
- **Ad Format**: Interstitial
- **Ad Unit Name**: World Costs Interstitial  
- **Expected Ad Unit ID**: ca-app-pub-3799584967407983/1033211807

##### **Rewarded Ad Unit:**
- **Ad Format**: Rewarded
- **Ad Unit Name**: World Costs Rewarded
- **Expected Ad Unit ID**: ca-app-pub-3799584967407983/5300978111

### **الخطوة 2: تحديث Ad Unit IDs في التطبيق**

#### **2.1 تحديث strings.xml:**
```xml
<!-- استبدل بـ Ad Unit IDs الحقيقية من AdMob Console -->
<string name="admob_banner_ad_unit_id">ca-app-pub-3799584967407983/XXXXXX</string>
<string name="admob_interstitial_ad_unit_id">ca-app-pub-3799584967407983/XXXXXX</string>
<string name="admob_rewarded_ad_unit_id">ca-app-pub-3799584967407983/XXXXXX</string>
```

#### **2.2 تعطيل وضع الاختبار:**
في `LauncherActivity.java`:
```java
// تغيير من true إلى false للإنتاج
adManager.setTestMode(false);
```

### **الخطوة 3: اختبار الإعلانات**

#### **3.1 اختبار محلي:**
1. ابني التطبيق: `.\gradlew.bat assembleDebug`
2. ثبت على جهاز اختبار: `adb install app-debug.apk`
3. تأكد من ظهور إعلانات الاختبار

#### **3.2 اختبار الإنتاج:**
1. غير `setTestMode(false)`
2. ابني إصدار الإنتاج: `.\gradlew.bat assembleRelease`
3. اختبر مع Ad Unit IDs الحقيقية

### **الخطوة 4: ربط AdMob بـ AdSense**

#### **4.1 إعداد الدفع:**
1. اذهب إلى AdMob Console → Payments
2. اربط حساب AdSense الموجود
3. أدخل معلومات الدفع والضرائب

#### **4.2 تحديد العتبة:**
- **الحد الأدنى للدفع**: $100 USD
- **طريقة الدفع**: تحويل بنكي أو Western Union
- **التوقيت**: شهرياً (إذا تم الوصول للحد الأدنى)

### **الخطوة 5: تحسين الأداء**

#### **5.1 مراقبة المقاييس:**
- **Impressions**: عدد مرات ظهور الإعلانات
- **Clicks**: عدد النقرات
- **CTR**: معدل النقر (Clicks/Impressions)
- **eCPM**: الأرباح لكل 1000 ظهور

#### **5.2 تحسين المواضع:**
- **Banner Ads**: أعلى وأسفل المحتوى
- **Interstitial Ads**: بين الصفحات أو بعد إجراءات
- **Rewarded Ads**: قبل المحتوى المميز

### **الخطوة 6: الامتثال والسياسات**

#### **6.1 سياسات AdMob:**
- ✅ لا تنقر على إعلاناتك الخاصة
- ✅ لا تطلب من المستخدمين النقر
- ✅ لا تضع إعلانات على محتوى محظور
- ✅ احترم خصوصية المستخدمين

#### **6.2 GDPR وخصوصية البيانات:**
- إضافة Privacy Policy للتطبيق
- إعلام المستخدمين عن جمع البيانات
- توفير خيارات إلغاء الاشتراك

## 🔧 **ملفات التكوين الحالية:**

### **AdMob App ID:**
```
ca-app-pub-3799584967407983~7787646685
```

### **Test Ad Unit IDs (للتطوير):**
```
Banner: ca-app-pub-3940256099942544/6300978111
Interstitial: ca-app-pub-3940256099942544/1033173712
Rewarded: ca-app-pub-3940256099942544/5224354917
```

### **Production Ad Unit IDs (للإنتاج):**
```
Banner: ca-app-pub-3799584967407983/6300978111
Interstitial: ca-app-pub-3799584967407983/1033211807
Rewarded: ca-app-pub-3799584967407983/5300978111
```

## 📊 **توقعات الأرباح:**

### **العوامل المؤثرة:**
- **عدد المستخدمين النشطين يومياً**
- **متوسط الجلسات لكل مستخدم**
- **الموقع الجغرافي للمستخدمين**
- **نوع المحتوى والفئة المستهدفة**

### **تقديرات أولية:**
- **1000 مستخدم نشط يومياً**: $5-15 يومياً
- **10000 مستخدم نشط يومياً**: $50-150 يومياً
- **100000 مستخدم نشط يومياً**: $500-1500 يومياً

*ملاحظة: هذه تقديرات تقريبية وقد تختلف حسب عوامل متعددة*

## 🚀 **خطة التنفيذ:**

### **الأسبوع الأول:**
- [ ] إعداد AdMob Console
- [ ] إنشاء Ad Units
- [ ] اختبار الإعلانات محلياً

### **الأسبوع الثاني:**
- [ ] تحديث التطبيق للإنتاج
- [ ] رفع على Google Play Store
- [ ] مراقبة الأداء الأولي

### **الأسبوع الثالث:**
- [ ] تحليل البيانات
- [ ] تحسين مواضع الإعلانات
- [ ] زيادة المحتوى لجذب المزيد من المستخدمين

## 📞 **الدعم والمساعدة:**

### **موارد مفيدة:**
- [AdMob Help Center](https://support.google.com/admob)
- [AdMob Best Practices](https://developers.google.com/admob/android/best-practices)
- [AdMob Policy Center](https://support.google.com/admob/answer/6128543)

### **في حالة المشاكل:**
1. تحقق من AdMob Console للأخطاء
2. راجع logs التطبيق
3. تأكد من صحة Ad Unit IDs
4. تحقق من اتصال الإنترنت

---

**🎯 بعد اتباع هذه الخطوات، ستبدأ في تحقيق أرباح من إعلانات AdMob!**
