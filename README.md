# World Costs - تطبيق تكاليف العالم

تطبيق Android بكامل الشاشة لمقارنة تكاليف المعيشة حول العالم.

## 🚀 كيفية تشغيل التطبيق

### الطريقة الأولى: تشغيل الخادم المحلي
1. تشغيل الخادم:
   ```bash
   python server.py
   ```
   أو استخدم الملف المختصر:
   ```bash
   start_server.bat
   ```

2. افتح المتصفح على: http://localhost:3000

### الطريقة الثانية: تثبيت التطبيق على Android

#### متطلبات:
- Android Studio مع SDK
- محاكي Android أو جهاز Android فعلي
- تفعيل "وضع المطور" و "تصحيح USB" (للجهاز الفعلي)

#### خطوات التثبيت:

1. **بناء التطبيق:**
   ```bash
   .\gradlew.bat assembleDebug
   ```

2. **تثبيت على محاكي:**
   ```bash
   adb install app/build/outputs/apk/debug/app-debug.apk
   ```

3. **تثبيت على جهاز فعلي:**
   - وصل الجهاز بالكمبيوتر
   - تأكد من تفعيل تصحيح USB
   - نفذ نفس أمر التثبيت أعلاه

## ✨ ميزات الشاشة الكاملة

التطبيق مُحسَّن للعمل بكامل الشاشة مع:

- **إخفاء شريط الحالة والتنقل**: ألوان سوداء شفافة
- **منع التكبير والتصغير**: تجربة مستقرة
- **دعم Safe Area**: متوافق مع الشاشات الحديثة
- **وضع Portrait**: مُقفل على الوضع العمودي
- **تجربة immersive**: مثل التطبيقات الأصلية

## 📁 هيكل المشروع

```
├── app/                          # كود التطبيق Android
│   ├── build.gradle             # إعدادات البناء
│   └── src/main/
│       ├── AndroidManifest.xml  # إعدادات التطبيق
│       └── res/
│           └── values/
│               └── styles.xml    # أنماط الشاشة الكاملة
├── server.py                    # الخادم المحلي
├── start_server.bat            # مختصر تشغيل الخادم
└── README.md                   # هذا الملف
```

## 🔧 إعدادات الشاشة الكاملة

### في Android:
- `android:theme="@style/Theme.Fullscreen"`
- `android:windowFullscreen="true"`
- `android:windowLayoutInDisplayCutoutMode="shortEdges"`
- `DISPLAY_MODE="fullscreen"`

### في الويب:
- `display: "fullscreen"` في Web App Manifest
- `viewport-fit=cover` في meta viewport
- JavaScript لإخفاء شريط العنوان

## 🌐 عناوين الوصول

- **المتصفح المحلي**: http://localhost:3000
- **محاكي Android**: http://********:3000
- **شبكة محلية**: http://[IP_ADDRESS]:3000

## 🛠️ استكشاف الأخطاء

### مشكلة: التطبيق لا يظهر بكامل الشاشة
- تأكد من أن الجهاز يدعم الوضع الغامر
- تحقق من إعدادات النظام للتطبيق

### مشكلة: لا يمكن الوصول للخادم من المحاكي
- تأكد من استخدام `********:3000` بدلاً من `localhost:3000`
- تحقق من إعدادات الشبكة في المحاكي

### مشكلة: فشل في بناء التطبيق
- تأكد من تثبيت Android SDK
- تحقق من إعدادات JAVA_HOME

## 📱 اختبار التطبيق

1. **في المتصفح**: افتح http://localhost:3000
2. **في المحاكي**: ثبت APK وشغل التطبيق
3. **على الجهاز**: ثبت APK عبر ADB أو نقل الملف

## 🎯 الخطوات التالية

- إضافة المزيد من الميزات للواجهة
- تحسين التصميم للشاشات المختلفة
- إضافة بيانات حقيقية لتكاليف المعيشة
- دعم اللغات المتعددة

---

**ملاحظة**: هذا التطبيق مُحسَّن للعمل بكامل الشاشة مثل التطبيقات الأصلية.
