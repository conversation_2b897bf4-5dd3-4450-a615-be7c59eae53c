/*
 * Copyright 2025 World Costs App
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.foodcana.worldcost;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.webkit.WebView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;
import com.google.android.gms.ads.rewarded.RewardedAd;
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback;

/**
 * AdManager handles all AdMob advertisements for the World Costs app
 */
public class AdManager {
    private static final String TAG = "AdManager";
    private static AdManager instance;
    
    private Context context;
    private InterstitialAd interstitialAd;
    private RewardedAd rewardedAd;
    private boolean isTestMode = false; // Set to true for testing
    
    private AdManager(Context context) {
        this.context = context.getApplicationContext();
    }
    
    public static synchronized AdManager getInstance(Context context) {
        if (instance == null) {
            instance = new AdManager(context);
        }
        return instance;
    }
    
    /**
     * Enable test mode for development
     */
    public void setTestMode(boolean testMode) {
        this.isTestMode = testMode;
        Log.d(TAG, "Test mode: " + testMode);
    }
    
    /**
     * Get the appropriate ad unit ID based on test mode
     */
    private String getAdUnitId(int realAdUnitId, int testAdUnitId) {
        return context.getString(isTestMode ? testAdUnitId : realAdUnitId);
    }
    
    /**
     * Load banner ad for WebView
     */
    public void loadBannerAd(AdView adView) {
        if (adView == null) return;
        
        AdRequest adRequest = new AdRequest.Builder().build();
        adView.loadAd(adRequest);
        Log.d(TAG, "Banner ad loaded");
    }
    
    /**
     * Load interstitial ad
     */
    public void loadInterstitialAd() {
        String adUnitId = getAdUnitId(
            R.string.admob_interstitial_ad_unit_id,
            R.string.test_interstitial_ad_unit_id
        );
        
        AdRequest adRequest = new AdRequest.Builder().build();
        
        InterstitialAd.load(context, adUnitId, adRequest,
            new InterstitialAdLoadCallback() {
                @Override
                public void onAdLoaded(InterstitialAd ad) {
                    interstitialAd = ad;
                    Log.d(TAG, "Interstitial ad loaded");
                    
                    interstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                        @Override
                        public void onAdDismissedFullScreenContent() {
                            interstitialAd = null;
                            loadInterstitialAd(); // Load next ad
                        }
                        
                        @Override
                        public void onAdFailedToShowFullScreenContent(com.google.android.gms.ads.AdError adError) {
                            interstitialAd = null;
                            Log.e(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                        }
                    });
                }
                
                @Override
                public void onAdFailedToLoad(LoadAdError loadAdError) {
                    interstitialAd = null;
                    Log.e(TAG, "Interstitial ad failed to load: " + loadAdError.getMessage());
                }
            });
    }
    
    /**
     * Show interstitial ad if available
     */
    public void showInterstitialAd(Activity activity) {
        if (interstitialAd != null && activity != null) {
            interstitialAd.show(activity);
        } else {
            Log.d(TAG, "Interstitial ad not ready");
            loadInterstitialAd(); // Try to load for next time
        }
    }
    
    /**
     * Load rewarded ad
     */
    public void loadRewardedAd() {
        String adUnitId = getAdUnitId(
            R.string.admob_rewarded_ad_unit_id,
            R.string.test_rewarded_ad_unit_id
        );
        
        AdRequest adRequest = new AdRequest.Builder().build();
        
        RewardedAd.load(context, adUnitId, adRequest,
            new RewardedAdLoadCallback() {
                @Override
                public void onAdLoaded(RewardedAd ad) {
                    rewardedAd = ad;
                    Log.d(TAG, "Rewarded ad loaded");
                    
                    rewardedAd.setFullScreenContentCallback(new FullScreenContentCallback() {
                        @Override
                        public void onAdDismissedFullScreenContent() {
                            rewardedAd = null;
                            loadRewardedAd(); // Load next ad
                        }
                        
                        @Override
                        public void onAdFailedToShowFullScreenContent(com.google.android.gms.ads.AdError adError) {
                            rewardedAd = null;
                            Log.e(TAG, "Rewarded ad failed to show: " + adError.getMessage());
                        }
                    });
                }
                
                @Override
                public void onAdFailedToLoad(LoadAdError loadAdError) {
                    rewardedAd = null;
                    Log.e(TAG, "Rewarded ad failed to load: " + loadAdError.getMessage());
                }
            });
    }
    
    /**
     * Show rewarded ad if available
     */
    public void showRewardedAd(Activity activity, RewardedAdCallback callback) {
        if (rewardedAd != null && activity != null) {
            rewardedAd.show(activity, rewardItem -> {
                Log.d(TAG, "User earned reward: " + rewardItem.getAmount() + " " + rewardItem.getType());
                if (callback != null) {
                    callback.onUserEarnedReward(rewardItem.getAmount(), rewardItem.getType());
                }
            });
        } else {
            Log.d(TAG, "Rewarded ad not ready");
            loadRewardedAd(); // Try to load for next time
        }
    }
    
    /**
     * Inject ads into WebView content
     */
    public void injectAdsIntoWebView(WebView webView) {
        if (webView == null) return;
        
        // JavaScript to inject banner ads into web content
        String adScript = 
            "javascript:(function() {" +
            "  var adContainer = document.createElement('div');" +
            "  adContainer.id = 'mobile-ad-container';" +
            "  adContainer.style.cssText = 'width:100%;height:50px;background:#f0f0f0;text-align:center;padding:5px;margin:10px 0;border:1px solid #ddd;';" +
            "  adContainer.innerHTML = '<div style=\"color:#666;font-size:12px;\">إعلان</div>';" +
            "  var content = document.querySelector('.container') || document.body;" +
            "  if (content && content.children.length > 1) {" +
            "    content.insertBefore(adContainer, content.children[1]);" +
            "  }" +
            "})()";
        
        webView.evaluateJavascript(adScript, null);
        Log.d(TAG, "Ad placeholder injected into WebView");
    }
    
    /**
     * Check if interstitial ad is ready
     */
    public boolean isInterstitialAdReady() {
        return interstitialAd != null;
    }
    
    /**
     * Check if rewarded ad is ready
     */
    public boolean isRewardedAdReady() {
        return rewardedAd != null;
    }
    
    /**
     * Callback interface for rewarded ads
     */
    public interface RewardedAdCallback {
        void onUserEarnedReward(int amount, String type);
    }
}
