/*
 * Copyright 2020 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.foodcana.worldcost;

import android.content.pm.ActivityInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.webkit.WebView;
import android.os.Handler;
import android.view.View;
import android.view.WindowManager;



public class LauncherActivity
        extends com.google.androidbrowserhelper.trusted.LauncherActivity {

    private AdManager adManager;
    private Handler adHandler;
    private static final int INTERSTITIAL_AD_INTERVAL = 60000; // 1 minute

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Force fullscreen before super.onCreate()
        try {
            requestWindowFeature(android.view.Window.FEATURE_NO_TITLE);
        } catch (Exception e) {
            // Ignore if already set
        }

        super.onCreate(savedInstanceState);

        // Force fullscreen mode
        enableFullscreenMode();

        // Initialize AdManager
        adManager = AdManager.getInstance(this);
        adManager.setTestMode(true); // Set to false for production

        // Load ads
        adManager.loadInterstitialAd();
        adManager.loadRewardedAd();

        // Setup ad timer
        adHandler = new Handler();
        scheduleInterstitialAd();

        // Setting an orientation crashes the app due to the transparent background on Android 8.0
        // Oreo and below. We only set the orientation on Oreo and above. This only affects the
        // splash screen and Chrome will still respect the orientation.
        // See https://github.com/GoogleChromeLabs/bubblewrap/issues/496 for details.
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        } else {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
    }

    /**
     * Enable true fullscreen mode to hide URL bar and browser controls
     */
    private void enableFullscreenMode() {
        try {
            // Force fullscreen before anything else
            requestWindowFeature(android.view.Window.FEATURE_NO_TITLE);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                // Most aggressive fullscreen mode
                int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY;

                // Add LOW_PROFILE for older devices
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
                    uiOptions |= View.SYSTEM_UI_FLAG_LOW_PROFILE;
                }

                getWindow().getDecorView().setSystemUiVisibility(uiOptions);
            }

            // Multiple fullscreen flags
            int windowFlags = WindowManager.LayoutParams.FLAG_FULLSCREEN;

            // Add additional flags for newer APIs
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                windowFlags |= WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                    | WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;
            }

            getWindow().setFlags(windowFlags, windowFlags);

            // Hide action bar if exists
            if (getActionBar() != null) {
                getActionBar().hide();
            }

            // Force hide title
            setTitle("");

            // Additional settings for newer APIs
            if (Build.VERSION.SDK_INT >= 28) { // API 28 = Android P
                try {
                    // Handle display cutout for API 28+ using reflection to avoid compile errors
                    WindowManager.LayoutParams layoutParams = getWindow().getAttributes();
                    Class<?> layoutParamsClass = layoutParams.getClass();
                    java.lang.reflect.Field field = layoutParamsClass.getField("layoutInDisplayCutoutMode");
                    field.setInt(layoutParams, 1); // LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES = 1
                    getWindow().setAttributes(layoutParams);
                } catch (Exception e) {
                    // Ignore if reflection fails
                }
            }

        } catch (Exception e) {
            // Fallback to basic fullscreen
            getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
            );
        }
    }

    @Override
    protected Uri getLaunchingUrl() {
        // Get the original launch Url.
        Uri uri = super.getLaunchingUrl();

        // Add error handling and validation
        if (uri == null) {
            // Fallback to default URL if something goes wrong
            uri = Uri.parse("https://www.worldcosts.com/");
        }

        return uri;
    }

    @Override
    protected void onResume() {
        super.onResume();

        // Force fullscreen mode again
        enableFullscreenMode();

        // Monitor and maintain fullscreen
        startFullscreenMonitor();

        // Setup ads when activity resumes
        setupWebViewForAds();

        // Resume ad scheduling
        if (adHandler != null) {
            scheduleInterstitialAd();
        }
    }

    /**
     * Monitor and maintain fullscreen mode
     */
    private void startFullscreenMonitor() {
        // Re-apply fullscreen every 500ms to ensure it stays
        Handler fullscreenHandler = new Handler();
        Runnable fullscreenRunnable = new Runnable() {
            @Override
            public void run() {
                enableFullscreenMode();
                fullscreenHandler.postDelayed(this, 500);
            }
        };
        fullscreenHandler.post(fullscreenRunnable);

        // Listen for system UI visibility changes
        getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(
            new View.OnSystemUiVisibilityChangeListener() {
                @Override
                public void onSystemUiVisibilityChange(int visibility) {
                    // Re-apply fullscreen when system UI becomes visible
                    if ((visibility & View.SYSTEM_UI_FLAG_FULLSCREEN) == 0) {
                        enableFullscreenMode();
                    }
                }
            }
        );
    }

    private void setupWebViewForAds() {
        // This will be called when the activity is ready
        // We'll inject ads through JavaScript after page load
        if (adManager != null) {
            // Schedule ad injection after a delay to ensure page is loaded
            adHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    // Inject ads via JavaScript
                    injectAdsViaJavaScript();
                }
            }, 3000); // 3 second delay
        }
    }

    private void injectAdsViaJavaScript() {
        // This method will inject ads through the TWA's WebView
        // Since we can't directly access the WebView in TWA, we'll use other methods
        // The ads will be handled by the web content itself
    }

    /**
     * Schedule interstitial ads to show periodically
     */
    private void scheduleInterstitialAd() {
        if (adHandler != null) {
            adHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (adManager != null && adManager.isInterstitialAdReady()) {
                        adManager.showInterstitialAd(LauncherActivity.this);
                    }
                    // Schedule next ad
                    scheduleInterstitialAd();
                }
            }, INTERSTITIAL_AD_INTERVAL);
        }
    }



    @Override
    protected void onPause() {
        super.onPause();
        // Pause ad scheduling
        if (adHandler != null) {
            adHandler.removeCallbacksAndMessages(null);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Clean up
        if (adHandler != null) {
            adHandler.removeCallbacksAndMessages(null);
            adHandler = null;
        }
        adManager = null;
    }
}
