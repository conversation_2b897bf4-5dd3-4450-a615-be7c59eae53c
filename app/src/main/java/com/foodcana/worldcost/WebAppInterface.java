/*
 * Copyright 2025 World Costs App
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.foodcana.worldcost;

import android.app.Activity;
import android.content.Context;
import android.webkit.JavascriptInterface;
import android.widget.Toast;

/**
 * JavaScript Interface to allow web content to interact with native Android features
 * including AdMob advertisements
 */
public class WebAppInterface {
    private Context context;
    private AdManager adManager;
    
    public WebAppInterface(Context context) {
        this.context = context;
        this.adManager = AdManager.getInstance(context);
    }
    
    /**
     * Show interstitial ad from JavaScript
     */
    @JavascriptInterface
    public void showInterstitialAd() {
        if (context instanceof Activity) {
            ((Activity) context).runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (adManager != null) {
                        adManager.showInterstitialAd((Activity) context);
                    }
                }
            });
        }
    }
    
    /**
     * Show rewarded ad from JavaScript
     */
    @JavascriptInterface
    public void showRewardedAd() {
        if (context instanceof Activity) {
            ((Activity) context).runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (adManager != null) {
                        adManager.showRewardedAd((Activity) context, new AdManager.RewardedAdCallback() {
                            @Override
                            public void onUserEarnedReward(int amount, String type) {
                                // Show reward message
                                Toast.makeText(context, 
                                    "تم الحصول على " + amount + " " + type + "!", 
                                    Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                }
            });
        }
    }
    
    /**
     * Check if interstitial ad is ready
     */
    @JavascriptInterface
    public boolean isInterstitialAdReady() {
        return adManager != null && adManager.isInterstitialAdReady();
    }
    
    /**
     * Check if rewarded ad is ready
     */
    @JavascriptInterface
    public boolean isRewardedAdReady() {
        return adManager != null && adManager.isRewardedAdReady();
    }
    
    /**
     * Load new interstitial ad
     */
    @JavascriptInterface
    public void loadInterstitialAd() {
        if (adManager != null) {
            adManager.loadInterstitialAd();
        }
    }
    
    /**
     * Load new rewarded ad
     */
    @JavascriptInterface
    public void loadRewardedAd() {
        if (adManager != null) {
            adManager.loadRewardedAd();
        }
    }
    
    /**
     * Show a toast message from JavaScript
     */
    @JavascriptInterface
    public void showToast(String message) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
    }
    
    /**
     * Get AdMob publisher ID
     */
    @JavascriptInterface
    public String getPublisherId() {
        return "pub-3799584967407983";
    }
    
    /**
     * Check if ads are enabled
     */
    @JavascriptInterface
    public boolean areAdsEnabled() {
        return true; // Can be controlled by user preferences
    }
}
