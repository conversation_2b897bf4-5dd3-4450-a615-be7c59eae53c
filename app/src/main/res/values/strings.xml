<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright 2021 Google Inc. All Rights Reserved.

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>


  <!--
    This variable below expresses the relationship between the app and the site,
    as documented in the TWA documentation at
    https://developers.google.com/web/updates/2017/10/using-twa#set_up_digital_asset_links_in_an_android_app
    and is injected into the AndroidManifest.xml
  -->
  <string name="assetStatements">
    [{
        \"relation\": [\"delegate_permission/common.handle_all_urls\"],
        \"target\": {
            \"namespace\": \"web\",
            \"site\": \"https://www.worldcosts.com\"
        }
    }]

  </string>

  <!-- AdMob Ad Unit IDs -->
  <string name="admob_app_id">ca-app-pub-3799584967407983~**********</string>
  <string name="admob_banner_ad_unit_id">ca-app-pub-3799584967407983/**********</string>
  <string name="admob_interstitial_ad_unit_id">ca-app-pub-3799584967407983/**********</string>
  <string name="admob_rewarded_ad_unit_id">ca-app-pub-3799584967407983/**********</string>

  <!-- Test Ad Unit IDs for development -->
  <string name="test_banner_ad_unit_id">ca-app-pub-3940256099942544/**********</string>
  <string name="test_interstitial_ad_unit_id">ca-app-pub-3940256099942544/1033173712</string>
  <string name="test_rewarded_ad_unit_id">ca-app-pub-3940256099942544/5224354917</string>

</resources>
