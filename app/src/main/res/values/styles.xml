<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme for fullscreen activity - Compatible with all API levels -->
    <style name="Theme.Fullscreen" parent="@android:style/Theme.Black.NoTitleBar.Fullscreen">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:statusBarColor">@android:color/black</item>
        <item name="android:navigationBarColor">@android:color/black</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowBackground">@android:color/black</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowShowWallpaper">false</item>
    </style>
</resources>
