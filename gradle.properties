# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true
android.useAndroidX=true

# Suppress Java compilation warnings
android.javaCompile.suppressSourceTargetDeprecationWarning=true

# Enable R8 full mode
android.enableR8.fullMode=true

# Enable build cache
org.gradle.caching=true

# Enable configuration cache
org.gradle.configuration-cache=true

# Kotlin code style
kotlin.code.style=official

# Enables namespacing of each library's R class
android.nonTransitiveRClass=true
