#!/usr/bin/env python3
"""
خادم ويب بسيط لاختبار تطبيق World Costs
"""

import http.server
import socketserver
import json
import os
from urllib.parse import urlparse, parse_qs

class WorldCostsHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)

        if parsed_path.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            html_content = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>World Costs - تكاليف العالم</title>
    <link rel="manifest" href="/manifest.webmanifest">
    <meta name="theme-color" content="#000000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            overflow-x: hidden;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #DBE3E8 0%, #CDD8DF 100%);
            min-height: 100vh;
            direction: rtl;
            /* إخفاء شريط التمرير */
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        body::-webkit-scrollbar {
            display: none;
        }
        .container {
            width: 100%;
            height: 100vh;
            margin: 0;
            background: white;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            /* إزالة الحدود والظلال للشاشة الكاملة */
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .welcome {
            text-align: center;
            font-size: 1.2em;
            color: #34495e;
            margin-bottom: 40px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: transform 0.3s ease;
        }
        .feature:hover {
            transform: translateY(-5px);
            border-color: #DBE3E8;
        }
        .feature h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin-top: 30px;
            border: 1px solid #c3e6cb;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 World Costs</h1>
        <div class="welcome">
            مرحباً بك في تطبيق تكاليف العالم!
        </div>

        <div class="features">
            <div class="feature">
                <h3>💰 مقارنة الأسعار</h3>
                <p>قارن أسعار المنتجات والخدمات حول العالم</p>
            </div>
            <div class="feature">
                <h3>🏙️ تكلفة المعيشة</h3>
                <p>اكتشف تكلفة المعيشة في مختلف المدن</p>
            </div>
            <div class="feature">
                <h3>📊 إحصائيات مفصلة</h3>
                <p>احصل على بيانات دقيقة ومحدثة</p>
            </div>
            <div class="feature">
                <h3>🔄 تحديثات مستمرة</h3>
                <p>بيانات محدثة باستمرار من مصادر موثوقة</p>
            </div>
        </div>

        <div style="text-align: center;">
            <button class="btn" onclick="showCosts()">عرض التكاليف</button>
            <button class="btn" onclick="compareCities()">مقارنة المدن</button>
        </div>

        <div class="status">
            ✅ الخادم المحلي يعمل بنجاح على المنفذ 3000
        </div>
    </div>

    <script>
        // إعداد الشاشة الكاملة
        function setupFullscreen() {
            // إخفاء شريط العنوان في المتصفحات المدعومة
            if (window.navigator && window.navigator.standalone !== undefined) {
                // iOS Safari
                document.documentElement.style.setProperty('--safe-area-inset-top', 'env(safe-area-inset-top)');
                document.documentElement.style.setProperty('--safe-area-inset-bottom', 'env(safe-area-inset-bottom)');
            }

            // منع التكبير والتصغير
            document.addEventListener('touchmove', function(e) {
                if (e.scale !== 1) {
                    e.preventDefault();
                }
            }, { passive: false });

            // منع السحب للتحديث
            document.addEventListener('touchstart', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault();
                }
            }, { passive: false });

            // إخفاء شريط العنوان عند التمرير
            let lastScrollTop = 0;
            window.addEventListener('scroll', function() {
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                if (scrollTop > lastScrollTop) {
                    // التمرير لأسفل - إخفاء شريط العنوان
                    if (window.scrollY > 50) {
                        document.body.style.paddingTop = '0px';
                    }
                }
                lastScrollTop = scrollTop;
            });
        }

        function showCosts() {
            alert('ميزة عرض التكاليف قيد التطوير!');
        }

        function compareCities() {
            alert('ميزة مقارنة المدن قيد التطوير!');
        }

        // تسجيل Service Worker إذا كان متاحاً
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(function(registration) {
                    console.log('Service Worker registered successfully');
                })
                .catch(function(error) {
                    console.log('Service Worker registration failed');
                });
        }

        // تشغيل إعداد الشاشة الكاملة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', setupFullscreen);
        window.addEventListener('load', setupFullscreen);
    </script>
</body>
</html>
            """
            self.wfile.write(html_content.encode('utf-8'))

        elif parsed_path.path == '/manifest.webmanifest':
            self.send_response(200)
            self.send_header('Content-type', 'application/manifest+json')
            self.end_headers()

            manifest = {
                "name": "World Costs",
                "short_name": "World Costs",
                "description": "تطبيق لمقارنة تكاليف المعيشة حول العالم",
                "start_url": "/",
                "display": "fullscreen",
                "orientation": "portrait",
                "background_color": "#000000",
                "theme_color": "#000000",
                "icons": [
                    {
                        "src": "/icon-192.png",
                        "sizes": "192x192",
                        "type": "image/png"
                    },
                    {
                        "src": "/icon-512.png",
                        "sizes": "512x512",
                        "type": "image/png"
                    }
                ]
            }

            self.wfile.write(json.dumps(manifest, ensure_ascii=False).encode('utf-8'))

        elif parsed_path.path == '/sw.js':
            self.send_response(200)
            self.send_header('Content-type', 'application/javascript')
            self.end_headers()

            sw_content = """
// Service Worker للتطبيق
const CACHE_NAME = 'world-costs-v1';
const urlsToCache = [
    '/',
    '/manifest.webmanifest'
];

self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});
            """
            self.wfile.write(sw_content.encode('utf-8'))

        else:
            # للملفات الأخرى، استخدم المعالج الافتراضي
            super().do_GET()

def start_server(port=3000):
    """بدء الخادم على المنفذ المحدد"""
    try:
        with socketserver.TCPServer(("", port), WorldCostsHandler) as httpd:
            print(f"🚀 خادم World Costs يعمل على http://localhost:{port}")
            print(f"📱 للاختبار مع المحاكي: http://********:{port}")
            print("⏹️  اضغط Ctrl+C لإيقاف الخادم")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")

if __name__ == "__main__":
    start_server()
